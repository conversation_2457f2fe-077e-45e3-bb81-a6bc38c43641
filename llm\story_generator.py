"""
Story Generator Module

Handles LLM API interactions via OpenRouter for generating manga stories.
Accepts vague prompts and returns structured story text.
"""

import os
import requests
from typing import Dict, Any, Optional
from dotenv import load_dotenv

load_dotenv()


class StoryGenerator:
    """
    Generates manga stories using OpenRouter LLM API.
    """
    
    def __init__(self, model: str = "anthropic/claude-3-sonnet"):
        """
        Initialize the story generator.
        
        Args:
            model: The LLM model to use via OpenRouter
        """
        self.api_key = os.getenv("OPENROUTER_API_KEY")
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
        self.model = model
        
        if not self.api_key:
            raise ValueError("OPENROUTER_API_KEY not found in environment variables")
    
    def generate_story(
        self, 
        prompt: str, 
        max_tokens: int = 2000,
        temperature: float = 0.7
    ) -> str:
        """
        Generate a manga story from a vague prompt.
        
        Args:
            prompt: The input prompt describing the desired story
            max_tokens: Maximum tokens for the response
            temperature: Creativity level (0.0 to 1.0)
            
        Returns:
            Generated story text
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": "You are a creative manga story writer. Generate engaging stories with clear scenes, dialogue, and visual descriptions suitable for manga panels."
                },
                {
                    "role": "user", 
                    "content": prompt
                }
            ],
            "max_tokens": max_tokens,
            "temperature": temperature
        }
        
        try:
            response = requests.post(self.base_url, json=payload, headers=headers)
            response.raise_for_status()
            
            data = response.json()
            return data["choices"][0]["message"]["content"]
            
        except requests.exceptions.RequestException as e:
            raise Exception(f"Error calling OpenRouter API: {e}")
        except KeyError as e:
            raise Exception(f"Unexpected API response format: {e}")
    
    def generate_structured_story(
        self, 
        prompt: str, 
        acts: int = 3,
        scenes_per_act: int = 3
    ) -> Dict[str, Any]:
        """
        Generate a structured manga story with acts and scenes.
        
        Args:
            prompt: The input prompt
            acts: Number of acts in the story
            scenes_per_act: Number of scenes per act
            
        Returns:
            Structured story data with acts, scenes, and metadata
        """
        structured_prompt = f"""
        Create a manga story based on: {prompt}
        
        Structure the story with {acts} acts and {scenes_per_act} scenes per act.
        For each scene, provide:
        - Scene description
        - Character dialogue
        - Visual elements for manga panels
        - Mood/atmosphere
        
        Format as a clear narrative with scene breaks.
        """
        
        story_text = self.generate_story(structured_prompt)
        
        # TODO: Parse the story text into structured format
        # For now, return basic structure
        return {
            "title": "Generated Manga Story",
            "prompt": prompt,
            "story_text": story_text,
            "acts": acts,
            "scenes_per_act": scenes_per_act,
            "metadata": {
                "model": self.model,
                "generated_at": None  # TODO: Add timestamp
            }
        }


def generate_manga_story(prompt: str, **kwargs) -> Dict[str, Any]:
    """
    Convenience function to generate a manga story.
    
    Args:
        prompt: Story prompt
        **kwargs: Additional arguments for StoryGenerator
        
    Returns:
        Generated story data
    """
    generator = StoryGenerator()
    return generator.generate_structured_story(prompt, **kwargs)


if __name__ == "__main__":
    # Example usage
    test_prompt = "A young ninja discovers they have magical powers in a modern city"
    story = generate_manga_story(test_prompt)
    print(f"Generated story: {story['story_text'][:200]}...")
