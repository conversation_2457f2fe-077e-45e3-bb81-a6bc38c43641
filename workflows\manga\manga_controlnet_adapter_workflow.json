{"1": {"inputs": {"ckpt_name": "v1-5-pruned-emaonly.ckpt"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "2": {"inputs": {"text": "masterpiece, best quality, manga style, black and white, high contrast, detailed line art, anime character", "clip": ["1", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "3": {"inputs": {"text": "blurry, low quality, distorted, ugly, bad anatomy, extra limbs, text, watermark, color, colored", "clip": ["1", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative)"}}, "4": {"inputs": {"width": 512, "height": 768, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "5": {"inputs": {"control_net_name": "control_sd15_openpose.pth"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model (OpenPose)"}}, "6": {"inputs": {"image": "pose_input_placeholder.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Pose Image"}}, "7": {"inputs": {"conditioning": ["2", 0], "control_net": ["5", 0], "image": ["6", 0], "strength": 1.0}, "class_type": "ControlNetApply", "_meta": {"title": "Apply ControlNet (Pose)"}}, "8": {"inputs": {"adapter_name": "t2iadapter_style_sd15.pth"}, "class_type": "T2IAdapterLoader", "_meta": {"title": "Load T2I Adapter (Style)"}}, "9": {"inputs": {"image": "style_input_placeholder.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Style Image"}}, "10": {"inputs": {"conditioning": ["7", 0], "t2i_adapter": ["8", 0], "image": ["9", 0], "strength": 0.8}, "class_type": "T2IAdapterApply", "_meta": {"title": "Apply T2I Adapter (Style)"}}, "11": {"inputs": {"seed": 42, "steps": 20, "cfg": 7.0, "sampler_name": "euler", "scheduler": "normal", "denoise": 1.0, "model": ["1", 0], "positive": ["10", 0], "negative": ["3", 0], "latent_image": ["4", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "12": {"inputs": {"samples": ["11", 0], "vae": ["1", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "13": {"inputs": {"filename_prefix": "manga_panel", "images": ["12", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}}