# Core dependencies for manga generation pipeline
requests>=2.31.0
python-dotenv>=1.0.0

# LLM and API interaction
openai>=1.0.0  # For OpenRouter compatibility
anthropic>=0.7.0  # Alternative LLM client

# Image processing and manipulation
Pillow>=10.0.0
opencv-python>=4.8.0

# PDF generation and document handling
reportlab>=4.0.0
PyPDF2>=3.0.0

# Data handling and utilities
numpy>=1.24.0
pandas>=2.0.0

# Web scraping and HTTP (for ComfyUI API)
aiohttp>=3.8.0
websockets>=11.0.0

# Configuration and environment
pydantic>=2.0.0
PyYAML>=6.0.0

# Logging and monitoring
rich>=13.0.0  # Enhanced console output
tqdm>=4.65.0  # Progress bars

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0

# Optional: ComfyUI API client (when available)
# comfyui-api>=0.1.0  # Placeholder - install when available

# Optional: Advanced image processing
# torch>=2.0.0  # For PyTorch-based image processing
# torchvision>=0.15.0
# transformers>=4.30.0  # For CLIP and other models

# Optional: Database support
# sqlalchemy>=2.0.0
# sqlite3  # Built-in with Python

# Optional: Advanced PDF features
# weasyprint>=59.0  # HTML to PDF conversion
# matplotlib>=3.7.0  # For charts and graphs in manga

# Optional: Audio processing (for future features)
# librosa>=0.10.0  # Audio analysis
# soundfile>=0.12.0  # Audio file I/O

# Optional: Video processing (for animated manga)
# moviepy>=1.0.3  # Video editing
# imageio>=2.31.0  # Image sequence to video

# Optional: Machine learning utilities
# scikit-learn>=1.3.0
# scipy>=1.11.0

# Optional: Cloud storage integration
# boto3>=1.28.0  # AWS S3
# google-cloud-storage>=2.10.0  # Google Cloud Storage

# Optional: Advanced text processing
# spacy>=3.6.0  # NLP processing
# nltk>=3.8.0  # Natural language toolkit

# Optional: Performance optimization
# numba>=0.57.0  # JIT compilation
# cython>=3.0.0  # C extensions

# Optional: GUI development (for future desktop app)
# tkinter  # Built-in with Python
# PyQt6>=6.5.0  # Advanced GUI framework
# streamlit>=1.25.0  # Web-based UI

# Optional: API framework (for web service)
# fastapi>=0.100.0
# uvicorn>=0.23.0

# Optional: Async processing
# celery>=5.3.0  # Task queue
# redis>=4.6.0  # Message broker

# Note: Some packages may require additional system dependencies
# For example:
# - OpenCV may require system libraries for video processing
# - WeasyPrint requires system libraries for HTML rendering
# - Some ML packages may require CUDA for GPU acceleration

# Installation notes:
# 1. Install core dependencies first: pip install -r requirements.txt
# 2. Uncomment and install optional dependencies as needed
# 3. For GPU acceleration, install PyTorch with CUDA support
# 4. For ComfyUI integration, install ComfyUI separately and ensure API access
# 5. Some packages may require specific Python versions (3.8+)

# Development workflow:
# pip install -r requirements.txt
# pip install -e .  # If you create a setup.py for the project

# Production deployment:
# Consider using pip-tools to generate locked requirements:
# pip-compile requirements.in
# pip install -r requirements.txt --no-deps
