#!/usr/bin/env python3
"""
Check ComfyUI Models

Checks what models are available in ComfyUI and tests a simple workflow.
"""

import requests
import json


def check_available_models():
    """Check what models are available in ComfyUI."""
    print("🔍 Checking Available Models in ComfyUI")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:8188"
    
    # Check checkpoints
    try:
        response = requests.get(f"{base_url}/object_info")
        if response.status_code == 200:
            object_info = response.json()
            
            # Check for CheckpointLoaderSimple
            if "CheckpointLoaderSimple" in object_info:
                checkpoints = object_info["CheckpointLoaderSimple"]["input"]["required"]["ckpt_name"][0]
                print("📦 Available Checkpoints:")
                for checkpoint in checkpoints:
                    print(f"   ✅ {checkpoint}")
                
                if not checkpoints:
                    print("   ❌ No checkpoints found!")
            
            # Check for ControlNet
            if "ControlNetLoader" in object_info:
                controlnets = object_info["ControlNetLoader"]["input"]["required"]["control_net_name"][0]
                print("\n🎮 Available ControlNets:")
                for controlnet in controlnets:
                    print(f"   ✅ {controlnet}")
                
                if not controlnets:
                    print("   ❌ No ControlNet models found!")
            
            # Check for T2I Adapter
            if "T2IAdapterLoader" in object_info:
                adapters = object_info["T2IAdapterLoader"]["input"]["required"]["adapter_name"][0]
                print("\n🔌 Available T2I Adapters:")
                for adapter in adapters:
                    print(f"   ✅ {adapter}")
                
                if not adapters:
                    print("   ❌ No T2I Adapter models found!")
            else:
                print("\n❌ T2IAdapterLoader node not available")
                print("   You may need to install T2I Adapter custom nodes")
            
            return True
            
    except Exception as e:
        print(f"❌ Error checking models: {e}")
        return False


def test_simple_workflow():
    """Test a very simple workflow to see if basic generation works."""
    print("\n🧪 Testing Simple Workflow")
    print("=" * 50)
    
    # Create a minimal workflow that should work with any SD model
    simple_workflow = {
        "1": {
            "inputs": {
                "ckpt_name": "v1-5-pruned-emaonly.ckpt"  # We'll need to change this
            },
            "class_type": "CheckpointLoaderSimple"
        },
        "2": {
            "inputs": {
                "text": "a simple test image",
                "clip": ["1", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "3": {
            "inputs": {
                "text": "blurry, low quality",
                "clip": ["1", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "4": {
            "inputs": {
                "width": 512,
                "height": 512,
                "batch_size": 1
            },
            "class_type": "EmptyLatentImage"
        },
        "5": {
            "inputs": {
                "seed": 12345,
                "steps": 10,
                "cfg": 7.0,
                "sampler_name": "euler",
                "scheduler": "normal",
                "denoise": 1.0,
                "model": ["1", 0],
                "positive": ["2", 0],
                "negative": ["3", 0],
                "latent_image": ["4", 0]
            },
            "class_type": "KSampler"
        },
        "6": {
            "inputs": {
                "samples": ["5", 0],
                "vae": ["1", 2]
            },
            "class_type": "VAEDecode"
        },
        "7": {
            "inputs": {
                "filename_prefix": "test_simple",
                "images": ["6", 0]
            },
            "class_type": "SaveImage"
        }
    }
    
    try:
        # First, get available checkpoints to use the right name
        response = requests.get("http://127.0.0.1:8188/object_info")
        if response.status_code == 200:
            object_info = response.json()
            checkpoints = object_info["CheckpointLoaderSimple"]["input"]["required"]["ckpt_name"][0]
            
            if checkpoints:
                # Use the first available checkpoint
                simple_workflow["1"]["inputs"]["ckpt_name"] = checkpoints[0]
                print(f"📦 Using checkpoint: {checkpoints[0]}")
                
                # Try to queue the workflow
                payload = {"prompt": simple_workflow}
                response = requests.post("http://127.0.0.1:8188/prompt", json=payload)
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ Simple workflow queued successfully!")
                    print(f"   Prompt ID: {result.get('prompt_id', 'unknown')}")
                    return True
                else:
                    print(f"❌ Workflow failed: {response.status_code}")
                    print(f"   Response: {response.text}")
                    return False
            else:
                print("❌ No checkpoints available for testing")
                return False
                
    except Exception as e:
        print(f"❌ Error testing workflow: {e}")
        return False


def suggest_fixes():
    """Suggest fixes based on what we found."""
    print("\n💡 Suggested Fixes")
    print("=" * 50)
    
    print("1. 📥 Download Required Models:")
    print("   - Go to ComfyUI/models/checkpoints/")
    print("   - Download: v1-5-pruned-emaonly.ckpt")
    print("   - From: https://huggingface.co/runwayml/stable-diffusion-v1-5")
    
    print("\n2. 🎮 ControlNet Models:")
    print("   - Go to ComfyUI/models/controlnet/")
    print("   - Download: control_sd15_openpose.pth")
    print("   - From: https://huggingface.co/lllyasviel/ControlNet")
    
    print("\n3. 🔌 T2I Adapter Setup:")
    print("   - Install custom nodes:")
    print("   cd ComfyUI/custom_nodes")
    print("   git clone https://github.com/TencentARC/T2I-Adapter")
    print("   - Restart ComfyUI")
    
    print("\n4. 🔄 After downloading models:")
    print("   - Restart ComfyUI")
    print("   - Run: python scripts/generate_panel.py --prompt 'test'")


def main():
    """Main function."""
    print("🔧 ComfyUI Model and Workflow Checker")
    print("=" * 60)
    
    # Check models
    models_ok = check_available_models()
    
    # Test simple workflow
    if models_ok:
        workflow_ok = test_simple_workflow()
    else:
        workflow_ok = False
    
    # Suggest fixes
    suggest_fixes()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 DIAGNOSIS")
    print("=" * 60)
    
    if models_ok and workflow_ok:
        print("✅ ComfyUI is properly set up!")
        print("🎯 Your manga generation should work now")
    else:
        print("⚠️  ComfyUI needs model setup")
        print("📥 Download the required models and try again")
    
    print("\n🔍 This explains why you're getting placeholder images:")
    print("   - ComfyUI is running ✅")
    print("   - But models are missing ❌")
    print("   - So workflows fail and fall back to placeholders")


if __name__ == "__main__":
    main()
