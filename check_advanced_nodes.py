#!/usr/bin/env python3
"""
Check Advanced ComfyUI Nodes

Checks what advanced nodes (ControlNet, T2I Adapter) are available in your ComfyUI setup.
"""

import requests
import json


def check_advanced_nodes():
    """Check what advanced nodes are available."""
    print("🔍 Checking Advanced ComfyUI Nodes")
    print("=" * 50)
    
    try:
        response = requests.get("http://127.0.0.1:8188/object_info")
        if response.status_code == 200:
            object_info = response.json()
            
            # Check ControlNet nodes
            print("🎮 ControlNet Nodes:")
            controlnet_nodes = [
                "ControlNetLoader",
                "ControlNetApply", 
                "ControlNetApplyAdvanced"
            ]
            
            for node in controlnet_nodes:
                if node in object_info:
                    print(f"   ✅ {node}")
                    if node == "ControlNetLoader":
                        models = object_info[node]["input"]["required"]["control_net_name"][0]
                        print(f"      Available models: {models}")
                else:
                    print(f"   ❌ {node}")
            
            # Check T2I Adapter nodes
            print("\n🔌 T2I Adapter Nodes:")
            adapter_nodes = [
                "T2IAdapterLoader",
                "T2IAdapterApply",
                "T2I-Adapter Loader",
                "T2I-Adapter Apply"
            ]
            
            found_adapter = False
            for node in adapter_nodes:
                if node in object_info:
                    print(f"   ✅ {node}")
                    found_adapter = True
                    try:
                        if "adapter_name" in object_info[node]["input"]["required"]:
                            models = object_info[node]["input"]["required"]["adapter_name"][0]
                            print(f"      Available models: {models}")
                        elif "t2i_adapter" in object_info[node]["input"]["required"]:
                            print(f"      Uses loaded adapter input")
                    except:
                        pass
                else:
                    print(f"   ❌ {node}")
            
            if not found_adapter:
                print("   ⚠️  No T2I Adapter nodes found")
                print("   💡 Try checking custom nodes or different node names")
            
            # Check preprocessor nodes
            print("\n🔧 Preprocessor Nodes:")
            preprocessor_nodes = [
                "OpenposePreprocessor",
                "CannyEdgePreprocessor", 
                "DepthPreprocessor",
                "LineArtPreprocessor"
            ]
            
            for node in preprocessor_nodes:
                if node in object_info:
                    print(f"   ✅ {node}")
                else:
                    print(f"   ❌ {node}")
            
            # List all available nodes (filtered for relevant ones)
            print("\n📋 All Available Nodes (filtered):")
            relevant_keywords = ["control", "adapter", "pose", "canny", "depth", "t2i"]
            relevant_nodes = []
            
            for node_name in object_info.keys():
                if any(keyword.lower() in node_name.lower() for keyword in relevant_keywords):
                    relevant_nodes.append(node_name)
            
            for node in sorted(relevant_nodes):
                print(f"   📄 {node}")
            
            return object_info
            
    except Exception as e:
        print(f"❌ Error checking nodes: {e}")
        return None


def suggest_workflow_improvements(object_info):
    """Suggest workflow improvements based on available nodes."""
    print("\n💡 Workflow Improvement Suggestions")
    print("=" * 50)
    
    if not object_info:
        return
    
    # Check what we can use
    has_controlnet = "ControlNetLoader" in object_info
    has_openpose = "OpenposePreprocessor" in object_info
    has_canny = "CannyEdgePreprocessor" in object_info
    
    # Look for T2I Adapter nodes with different naming patterns
    t2i_nodes = [node for node in object_info.keys() if "adapter" in node.lower() or "t2i" in node.lower()]
    has_t2i = len(t2i_nodes) > 0
    
    print("🎯 Recommended Workflow Features:")
    
    if has_controlnet:
        print("   ✅ Use ControlNet for pose guidance")
        controlnet_models = object_info["ControlNetLoader"]["input"]["required"]["control_net_name"][0]
        if "control_sd15_openpose.pth" in controlnet_models:
            print("      - OpenPose ControlNet available")
        if "control_sd15_canny.pth" in controlnet_models:
            print("      - Canny ControlNet available")
    
    if has_t2i:
        print("   ✅ Use T2I Adapter for style conditioning")
        print(f"      - Available T2I nodes: {t2i_nodes}")
    
    if has_openpose:
        print("   ✅ Use OpenPose preprocessor for pose extraction")
    
    if has_canny:
        print("   ✅ Use Canny preprocessor for line art")
    
    print(f"\n🚀 Quality Level: {'HIGH' if (has_controlnet and has_t2i) else 'MEDIUM' if has_controlnet else 'BASIC'}")


def main():
    """Main function."""
    print("🔧 Advanced ComfyUI Node Checker")
    print("=" * 60)
    
    object_info = check_advanced_nodes()
    suggest_workflow_improvements(object_info)
    
    print("\n" + "=" * 60)
    print("📊 ANALYSIS COMPLETE")
    print("=" * 60)
    print("Now I'll create the optimal workflow based on your available features!")


if __name__ == "__main__":
    main()
