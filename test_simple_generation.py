#!/usr/bin/env python3
"""
Test Simple Generation

Tests the simplest possible image generation with ComfyUI to verify it works.
"""

import requests
import json
import time


def test_basic_generation():
    """Test the most basic text-to-image generation."""
    print("🧪 Testing Basic Text-to-Image Generation")
    print("=" * 50)
    
    # Ultra-simple workflow
    workflow = {
        "1": {
            "inputs": {
                "ckpt_name": "v1-5-pruned-emaonly.safetensors"
            },
            "class_type": "CheckpointLoaderSimple"
        },
        "2": {
            "inputs": {
                "text": "a ninja jumping in the air, manga style, black and white",
                "clip": ["1", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "3": {
            "inputs": {
                "text": "blurry, low quality, color",
                "clip": ["1", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "4": {
            "inputs": {
                "width": 512,
                "height": 768,
                "batch_size": 1
            },
            "class_type": "EmptyLatentImage"
        },
        "5": {
            "inputs": {
                "seed": 12345,
                "steps": 15,
                "cfg": 7.0,
                "sampler_name": "euler",
                "scheduler": "normal",
                "denoise": 1.0,
                "model": ["1", 0],
                "positive": ["2", 0],
                "negative": ["3", 0],
                "latent_image": ["4", 0]
            },
            "class_type": "KSampler"
        },
        "6": {
            "inputs": {
                "samples": ["5", 0],
                "vae": ["1", 2]
            },
            "class_type": "VAEDecode"
        },
        "7": {
            "inputs": {
                "filename_prefix": "test_ninja",
                "images": ["6", 0]
            },
            "class_type": "SaveImage"
        }
    }
    
    try:
        print("📤 Sending workflow to ComfyUI...")
        
        payload = {"prompt": workflow}
        response = requests.post("http://127.0.0.1:8188/prompt", json=payload)
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            prompt_id = result.get("prompt_id")
            print(f"✅ Workflow queued successfully!")
            print(f"🆔 Prompt ID: {prompt_id}")
            
            # Wait a bit and check if it completed
            print("⏳ Waiting for generation...")
            time.sleep(10)
            
            # Check history
            history_response = requests.get(f"http://127.0.0.1:8188/history/{prompt_id}")
            if history_response.status_code == 200:
                history = history_response.json()
                if prompt_id in history:
                    print("✅ Generation completed!")
                    return True
                else:
                    print("⏳ Still processing...")
                    return True
            
        else:
            print(f"❌ Workflow failed!")
            print(f"📄 Response: {response.text}")
            
            # Try to parse error details
            try:
                error_data = response.json()
                print(f"🔍 Error details: {json.dumps(error_data, indent=2)}")
            except:
                pass
            
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_workflow_validation():
    """Test if our workflow structure is valid."""
    print("\n🔍 Testing Workflow Validation")
    print("=" * 50)
    
    # Test with ComfyUI's validate endpoint if available
    try:
        response = requests.get("http://127.0.0.1:8188/object_info")
        if response.status_code == 200:
            object_info = response.json()
            
            # Check required nodes
            required_nodes = [
                "CheckpointLoaderSimple",
                "CLIPTextEncode", 
                "EmptyLatentImage",
                "KSampler",
                "VAEDecode",
                "SaveImage"
            ]
            
            print("📋 Checking required nodes:")
            all_available = True
            for node in required_nodes:
                if node in object_info:
                    print(f"   ✅ {node}")
                else:
                    print(f"   ❌ {node} - MISSING!")
                    all_available = False
            
            return all_available
            
    except Exception as e:
        print(f"❌ Error checking nodes: {e}")
        return False


def main():
    """Main test function."""
    print("🧪 Simple ComfyUI Generation Test")
    print("=" * 60)
    
    # Test workflow validation
    nodes_ok = test_workflow_validation()
    
    if nodes_ok:
        # Test basic generation
        generation_ok = test_basic_generation()
        
        if generation_ok:
            print("\n🎉 SUCCESS!")
            print("ComfyUI is working properly for basic generation.")
            print("The issue with manga generation might be in the complex workflows.")
        else:
            print("\n⚠️ BASIC GENERATION FAILED")
            print("There's an issue with the fundamental ComfyUI setup.")
    else:
        print("\n❌ MISSING NODES")
        print("ComfyUI is missing required nodes.")
    
    print("\n💡 If this test passes, we can fix the manga workflows.")
    print("If this test fails, we need to fix the basic ComfyUI setup first.")


if __name__ == "__main__":
    main()
