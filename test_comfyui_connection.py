#!/usr/bin/env python3
"""
ComfyUI Connection Test

Tests if ComfyUI is running and accessible for manga generation.
"""

import requests
import json
from pathlib import Path


def test_comfyui_connection():
    """Test if ComfyUI server is running."""
    print("🔌 Testing ComfyUI Connection")
    print("=" * 40)
    
    comfyui_url = "http://127.0.0.1:8188"
    
    try:
        # Test basic connection
        response = requests.get(f"{comfyui_url}/system_stats", timeout=5)
        
        if response.status_code == 200:
            print("✅ ComfyUI server is running!")
            
            # Get system stats
            stats = response.json()
            print(f"📊 System Stats:")
            for key, value in stats.items():
                print(f"   {key}: {value}")
            
            return True
            
        else:
            print(f"❌ ComfyUI responded with status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ ComfyUI server is not running")
        print(f"   Expected URL: {comfyui_url}")
        print("\n💡 To start ComfyUI:")
        print("   1. cd ComfyUI")
        print("   2. python main.py")
        return False
        
    except Exception as e:
        print(f"❌ Error connecting to ComfyUI: {e}")
        return False


def test_required_models():
    """Test if required models are available."""
    print("\n📦 Testing Required Models")
    print("=" * 40)
    
    # This would require ComfyUI API to check models
    # For now, just show what's needed
    
    required_models = {
        "Checkpoint": "v1-5-pruned-emaonly.ckpt",
        "ControlNet": "control_sd15_openpose.pth", 
        "T2I Adapter": "t2iadapter_style_sd15.pth"
    }
    
    print("Required models for manga generation:")
    for model_type, model_name in required_models.items():
        print(f"   📄 {model_type}: {model_name}")
    
    print("\n💾 Download locations:")
    print("   Checkpoints: https://huggingface.co/runwayml/stable-diffusion-v1-5")
    print("   ControlNet: https://huggingface.co/lllyasviel/ControlNet")
    print("   T2I Adapter: https://huggingface.co/TencentARC/T2I-Adapter")


def show_setup_instructions():
    """Show complete setup instructions."""
    print("\n🛠️ Complete Setup Instructions")
    print("=" * 40)
    
    instructions = [
        "1. Install ComfyUI:",
        "   git clone https://github.com/comfyanonymous/ComfyUI.git",
        "   cd ComfyUI",
        "   pip install -r requirements.txt",
        "",
        "2. Download models to ComfyUI/models/ directories",
        "",
        "3. Install custom nodes:",
        "   cd ComfyUI/custom_nodes",
        "   git clone https://github.com/Fannovel16/comfyui_controlnet_aux",
        "",
        "4. Start ComfyUI:",
        "   cd ComfyUI",
        "   python main.py",
        "",
        "5. Test manga generation:",
        "   python scripts/generate_panel.py --prompt 'ninja jumping'"
    ]
    
    for instruction in instructions:
        print(instruction)


def test_placeholder_vs_real():
    """Explain the difference between placeholder and real generation."""
    print("\n🎨 Placeholder vs Real Generation")
    print("=" * 40)
    
    print("📝 CURRENT (Placeholder Mode):")
    print("   - Text-based panels with prompt description")
    print("   - No actual artwork generated")
    print("   - Works without ComfyUI")
    print("   - Good for testing pipeline")
    
    print("\n🎨 WITH COMFYUI (Real Mode):")
    print("   - Actual manga-style artwork")
    print("   - Pose-guided character generation")
    print("   - Style-conditioned output")
    print("   - High-quality 512x768 images")
    
    print("\n🔄 The same command will work for both:")
    print("   python scripts/generate_panel.py \\")
    print("     --prompt 'ninja jumping' \\")
    print("     --pose 'generate:jumping pose' \\")
    print("     --style 'process:naruto'")


def main():
    """Main test function."""
    print("🧪 ComfyUI Setup and Connection Test")
    print("=" * 50)
    
    # Test connection
    is_connected = test_comfyui_connection()
    
    # Show model requirements
    test_required_models()
    
    # Show setup instructions
    show_setup_instructions()
    
    # Explain modes
    test_placeholder_vs_real()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 SUMMARY")
    print("=" * 50)
    
    if is_connected:
        print("✅ ComfyUI is running - ready for real manga generation!")
        print("🎯 Try running your manga generation command again")
    else:
        print("⚠️  ComfyUI is not running - currently in placeholder mode")
        print("🛠️ Follow the setup instructions above to enable real generation")
    
    print("\n💡 Your current images are placeholders because ComfyUI isn't set up yet.")
    print("   Once ComfyUI is running, you'll get actual manga artwork!")


if __name__ == "__main__":
    main()
