#!/usr/bin/env python3
"""
Test ControlNet Workflow

Tests different ControlNet configurations to find what works.
"""

import requests
import json


def test_single_controlnet():
    """Test single ControlNet workflow."""
    print("🧪 Testing Single ControlNet Workflow")
    print("=" * 50)
    
    workflow = {
        "1": {
            "inputs": {
                "ckpt_name": "v1-5-pruned-emaonly.safetensors"
            },
            "class_type": "CheckpointLoaderSimple"
        },
        "2": {
            "inputs": {
                "text": "ninja jumping, manga style, black and white, high quality",
                "clip": ["1", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "3": {
            "inputs": {
                "text": "blurry, low quality, color",
                "clip": ["1", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "4": {
            "inputs": {
                "width": 512,
                "height": 768,
                "batch_size": 1
            },
            "class_type": "EmptyLatentImage"
        },
        "5": {
            "inputs": {
                "control_net_name": "control_sd15_openpose.pth"
            },
            "class_type": "ControlNetLoader"
        },
        "6": {
            "inputs": {
                "image": "assets/poses/jumping_pose.png",
                "upload": "image"
            },
            "class_type": "LoadImage"
        },
        "7": {
            "inputs": {
                "conditioning": ["2", 0],
                "control_net": ["5", 0],
                "image": ["6", 0],
                "strength": 1.0
            },
            "class_type": "ControlNetApply"
        },
        "8": {
            "inputs": {
                "seed": 12345,
                "steps": 25,
                "cfg": 7.5,
                "sampler_name": "dpmpp_2m_karras",
                "scheduler": "karras",
                "denoise": 1.0,
                "model": ["1", 0],
                "positive": ["7", 0],
                "negative": ["3", 0],
                "latent_image": ["4", 0]
            },
            "class_type": "KSampler"
        },
        "9": {
            "inputs": {
                "samples": ["8", 0],
                "vae": ["1", 2]
            },
            "class_type": "VAEDecode"
        },
        "10": {
            "inputs": {
                "filename_prefix": "test_controlnet",
                "images": ["9", 0]
            },
            "class_type": "SaveImage"
        }
    }
    
    try:
        payload = {"prompt": workflow}
        response = requests.post("http://127.0.0.1:8188/prompt", json=payload)
        
        if response.status_code == 200:
            print("✅ Single ControlNet workflow works!")
            result = response.json()
            print(f"🆔 Prompt ID: {result.get('prompt_id')}")
            return True
        else:
            print(f"❌ Single ControlNet failed: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_canny_node():
    """Test if Canny node exists."""
    print("\n🔍 Testing Canny Node Availability")
    print("=" * 50)
    
    try:
        response = requests.get("http://127.0.0.1:8188/object_info")
        if response.status_code == 200:
            object_info = response.json()
            
            canny_nodes = [node for node in object_info.keys() if "canny" in node.lower()]
            
            if canny_nodes:
                print("✅ Canny nodes found:")
                for node in canny_nodes:
                    print(f"   📄 {node}")
                return canny_nodes[0]  # Return first available
            else:
                print("❌ No Canny nodes found")
                print("💡 Available preprocessor-like nodes:")
                
                # Look for any preprocessing nodes
                preprocess_nodes = [node for node in object_info.keys() 
                                  if any(keyword in node.lower() for keyword in 
                                        ['preprocess', 'edge', 'detect', 'extract'])]
                
                for node in preprocess_nodes[:10]:  # Show first 10
                    print(f"   📄 {node}")
                
                return None
                
    except Exception as e:
        print(f"❌ Error checking Canny: {e}")
        return None


def create_optimized_workflow():
    """Create optimized single ControlNet workflow."""
    print("\n🚀 Creating Optimized Single ControlNet Workflow")
    print("=" * 50)
    
    workflow = {
        "1": {
            "inputs": {
                "ckpt_name": "v1-5-pruned-emaonly.safetensors"
            },
            "class_type": "CheckpointLoaderSimple",
            "_meta": {"title": "Load Checkpoint"}
        },
        "2": {
            "inputs": {
                "text": "{prompt}, masterpiece, best quality, manga style, black and white, high contrast, detailed line art, anime character, monochrome, clean lines, professional manga artwork, dynamic pose, expressive character, speed lines, action scene",
                "clip": ["1", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "Enhanced Manga Prompt"}
        },
        "3": {
            "inputs": {
                "text": "blurry, low quality, distorted, ugly, bad anatomy, extra limbs, text, watermark, color, colored, multiple people, cropped, bad hands, bad face, deformed, mutation, worst quality, jpeg artifacts, signature, username, realistic, photo",
                "clip": ["1", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "Enhanced Negative Prompt"}
        },
        "4": {
            "inputs": {
                "width": 512,
                "height": 768,
                "batch_size": 1
            },
            "class_type": "EmptyLatentImage",
            "_meta": {"title": "Manga Panel Size"}
        },
        "5": {
            "inputs": {
                "control_net_name": "control_sd15_openpose.pth"
            },
            "class_type": "ControlNetLoader",
            "_meta": {"title": "Load OpenPose ControlNet"}
        },
        "6": {
            "inputs": {
                "image": "{pose_image_path}",
                "upload": "image"
            },
            "class_type": "LoadImage",
            "_meta": {"title": "Load Pose Reference"}
        },
        "7": {
            "inputs": {
                "conditioning": ["2", 0],
                "control_net": ["5", 0],
                "image": ["6", 0],
                "strength": 1.0
            },
            "class_type": "ControlNetApply",
            "_meta": {"title": "Apply Pose Control"}
        },
        "8": {
            "inputs": {
                "seed": "{seed}",
                "steps": 30,
                "cfg": 8.0,
                "sampler_name": "dpmpp_2m_karras",
                "scheduler": "karras",
                "denoise": 1.0,
                "model": ["1", 0],
                "positive": ["7", 0],
                "negative": ["3", 0],
                "latent_image": ["4", 0]
            },
            "class_type": "KSampler",
            "_meta": {"title": "High Quality Sampling"}
        },
        "9": {
            "inputs": {
                "samples": ["8", 0],
                "vae": ["1", 2]
            },
            "class_type": "VAEDecode",
            "_meta": {"title": "VAE Decode"}
        },
        "10": {
            "inputs": {
                "filename_prefix": "hq_manga_{timestamp}",
                "images": ["9", 0]
            },
            "class_type": "SaveImage",
            "_meta": {"title": "Save High Quality Panel"}
        }
    }
    
    # Save the optimized workflow
    import json
    from pathlib import Path
    
    workflow_path = Path("workflows/manga/optimized_controlnet_workflow.json")
    with open(workflow_path, 'w') as f:
        json.dump(workflow, f, indent=2)
    
    print(f"✅ Created optimized workflow: {workflow_path}")
    return workflow


def main():
    """Main test function."""
    print("🔧 ControlNet Workflow Testing")
    print("=" * 60)
    
    # Test single ControlNet
    single_works = test_single_controlnet()
    
    # Check Canny availability
    canny_node = test_canny_node()
    
    # Create optimized workflow
    optimized_workflow = create_optimized_workflow()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS")
    print("=" * 60)
    
    if single_works:
        print("✅ Single ControlNet: WORKING")
        print("🎯 Quality Level: HIGH (with pose control)")
    else:
        print("❌ Single ControlNet: FAILED")
    
    if canny_node:
        print(f"✅ Canny Processing: Available ({canny_node})")
    else:
        print("⚠️  Canny Processing: Not available (dual ControlNet won't work)")
    
    print("\n💡 Recommendation:")
    if single_works:
        print("   Use optimized single ControlNet workflow for high quality")
        print("   30 steps + DPM++ 2M Karras + enhanced prompts = excellent results")
    else:
        print("   Stick with basic workflow until ControlNet issues are resolved")


if __name__ == "__main__":
    main()
