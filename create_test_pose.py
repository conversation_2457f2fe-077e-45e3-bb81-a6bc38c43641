#!/usr/bin/env python3
"""
Create Test Pose Image

Creates a simple pose image for testing the high-quality workflow.
"""

from PIL import Image, ImageDraw
from pathlib import Path


def create_jumping_pose():
    """Create a simple jumping pose image."""
    print("🎨 Creating test jumping pose image...")
    
    # Create white background
    width, height = 512, 768
    image = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(image)
    
    # Draw a simple stick figure in jumping pose
    # Head
    head_x, head_y = width // 2, 150
    head_radius = 30
    draw.ellipse([head_x - head_radius, head_y - head_radius, 
                  head_x + head_radius, head_y + head_radius], 
                 outline='black', width=3)
    
    # Body
    body_start_y = head_y + head_radius
    body_end_y = body_start_y + 120
    draw.line([head_x, body_start_y, head_x, body_end_y], fill='black', width=3)
    
    # Arms (spread wide for jumping)
    arm_y = body_start_y + 40
    arm_length = 80
    # Left arm
    draw.line([head_x, arm_y, head_x - arm_length, arm_y - 30], fill='black', width=3)
    # Right arm  
    draw.line([head_x, arm_y, head_x + arm_length, arm_y - 30], fill='black', width=3)
    
    # Legs (bent for jumping)
    leg_start_y = body_end_y
    leg_length = 100
    # Left leg
    draw.line([head_x, leg_start_y, head_x - 40, leg_start_y + leg_length], fill='black', width=3)
    # Right leg
    draw.line([head_x, leg_start_y, head_x + 40, leg_start_y + leg_length], fill='black', width=3)
    
    # Add some motion lines
    for i in range(5):
        y = 400 + i * 20
        draw.line([50, y, 150, y], fill='gray', width=2)
        draw.line([350, y, 450, y], fill='gray', width=2)
    
    # Save the pose
    pose_dir = Path("assets/poses")
    pose_dir.mkdir(parents=True, exist_ok=True)
    
    pose_path = pose_dir / "jumping_pose.png"
    image.save(pose_path)
    
    print(f"✅ Created jumping pose: {pose_path}")
    return str(pose_path)


def create_style_reference():
    """Create a simple manga style reference."""
    print("🎨 Creating manga style reference...")
    
    width, height = 512, 512
    image = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(image)
    
    # Draw manga-style elements
    # Panel border
    draw.rectangle([20, 20, width-20, height-20], outline='black', width=4)
    
    # Speed lines
    center_x, center_y = width // 2, height // 2
    for angle in range(0, 360, 15):
        import math
        x1 = center_x + 50 * math.cos(math.radians(angle))
        y1 = center_y + 50 * math.sin(math.radians(angle))
        x2 = center_x + 150 * math.cos(math.radians(angle))
        y2 = center_y + 150 * math.sin(math.radians(angle))
        draw.line([x1, y1, x2, y2], fill='black', width=2)
    
    # Save the style reference
    style_dir = Path("assets/styles")
    style_dir.mkdir(parents=True, exist_ok=True)
    
    style_path = style_dir / "manga_lines.png"
    image.save(style_path)
    
    print(f"✅ Created style reference: {style_path}")
    return str(style_path)


def main():
    """Create test assets."""
    print("🛠️ Creating Test Assets for High-Quality Generation")
    print("=" * 60)
    
    pose_path = create_jumping_pose()
    style_path = create_style_reference()
    
    print("\n✅ Test assets created!")
    print(f"📄 Pose: {pose_path}")
    print(f"🎨 Style: {style_path}")
    
    print("\n💡 Now you can test with:")
    print("python scripts/generate_panel.py \\")
    print("  --prompt 'ninja jumping with speed lines' \\")
    print("  --pose 'jumping_pose.png' \\")
    print("  --style 'manga_lines.png'")


if __name__ == "__main__":
    main()
